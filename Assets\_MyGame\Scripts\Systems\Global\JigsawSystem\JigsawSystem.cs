using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class JigsawSystem : GlobalSystem
{
    private JigsawSaveData saveData;
    private List<InfoJigsaw> jigsawConfigs;
    private Dictionary<int, JigsawData> jigsawDataDict = new Dictionary<int, JigsawData>();

    public event Action<int> OnJigsawUnlocked;
    public event Action<int> OnPieceCountChanged;

    private const string SAVE_KEY = "Jigsaw_SaveData";

    protected override void OnInit()
    {
        jigsawConfigs = ConfigJigsaw.GetJigsaws();
        LoadData();

        if (saveData == null)
        {
            InitializeDefaultData();
        }
        else
        {
            SyncJigsawData();
        }
    }

    private void InitializeDefaultData()
    {
        saveData = new JigsawSaveData();
        saveData.currentPieceCount = 0;
        saveData.unlockedJigsaws = new Dictionary<int, bool>();

        foreach (var config in jigsawConfigs)
        {
            saveData.unlockedJigsaws[config.id] = false;
            jigsawDataDict[config.id] = new JigsawData(config.id);
        }

        SaveData();
    }

    private void SyncJigsawData()
    {
        saveData.OnAfterDeserialize();
        jigsawDataDict.Clear();

        foreach (var config in jigsawConfigs)
        {
            var jigsawData = new JigsawData(config.id);
            jigsawData.isUnlocked = saveData.unlockedJigsaws.ContainsKey(config.id) && saveData.unlockedJigsaws[config.id];
            jigsawDataDict[config.id] = jigsawData;
        }
    }

    public void AddPieces(int count)
    {
        if (count <= 0) return;

        saveData.currentPieceCount += count;
        CheckForNewUnlocks();
        OnPieceCountChanged?.Invoke(saveData.currentPieceCount);
        SaveData();
    }

    public bool ConsumePieces(int count)
    {
        if (count <= 0 || saveData.currentPieceCount < count) return false;

        saveData.currentPieceCount -= count;
        OnPieceCountChanged?.Invoke(saveData.currentPieceCount);
        SaveData();
        return true;
    }

    public int GetCurrentPieceCount(int pictureId)
    {
        return saveData?.currentPieceCount ?? 0;
    }

    public bool IsJigsawUnlocked(int jigsawId)
    {
        return jigsawDataDict.ContainsKey(jigsawId) && jigsawDataDict[jigsawId].isUnlocked;
    }

    public JigsawData GetJigsawData(int jigsawId)
    {
        return jigsawDataDict.ContainsKey(jigsawId) ? jigsawDataDict[jigsawId] : null;
    }

    public List<JigsawData> GetAllJigsawData()
    {
        return jigsawDataDict.Values.ToList();
    }

    public List<JigsawData> GetUnlockedJigsaws()
    {
        return jigsawDataDict.Values.Where(data => data.isUnlocked).ToList();
    }

    public List<JigsawData> GetLockedJigsaws()
    {
        return jigsawDataDict.Values.Where(data => !data.isUnlocked).ToList();
    }

    public bool CanUnlockJigsaw(int jigsawId)
    {
        var jigsawData = GetJigsawData(jigsawId);
        return jigsawData != null && !jigsawData.isUnlocked && jigsawData.CanUnlock(saveData.currentPieceCount);
    }

    public bool TryUnlockJigsaw(int jigsawId)
    {
        if (!CanUnlockJigsaw(jigsawId)) return false;

        var jigsawData = jigsawDataDict[jigsawId];
        if (ConsumePieces(jigsawData.requiredPieces))
        {
            jigsawData.isUnlocked = true;
            saveData.unlockedJigsaws[jigsawId] = true;
            OnJigsawUnlocked?.Invoke(jigsawId);
            SaveData();
            return true;
        }

        return false;
    }

    private void CheckForNewUnlocks()
    {
        foreach (var jigsawData in jigsawDataDict.Values)
        {
            if (!jigsawData.isUnlocked && jigsawData.CanUnlock(saveData.currentPieceCount))
            {
                // 这里只是检查可以解锁，不自动解锁，需要玩家手动解锁
            }
        }
    }

    public override string GetDebugInfo()
    {
        if (saveData == null)
            return "SaveData: Null";

        var unlockedCount = jigsawDataDict.Values.Count(data => data.isUnlocked);
        var totalCount = jigsawDataDict.Count;
        return $"Pieces: {saveData.currentPieceCount}, Unlocked: {unlockedCount}/{totalCount}";
    }

    private void SaveData()
    {
        saveData.OnBeforeSerialize();
        string json = JsonUtility.ToJson(saveData);
        PlayerPrefsManager.Set(SAVE_KEY, json);
        PlayerPrefsManager.Save();
    }

    private void LoadData()
    {
        if (PlayerPrefsManager.HasKey(SAVE_KEY))
        {
            string json = PlayerPrefsManager.GetString(SAVE_KEY);
            saveData = JsonUtility.FromJson<JigsawSaveData>(json);
        }
    }
}
