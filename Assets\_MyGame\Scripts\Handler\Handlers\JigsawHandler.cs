﻿internal class JigsawHandler : HandlerBase
{
    private JigsawPanel jigsawPanel;
    public override void OnEnter(object data)
    {
        LoadingPanel.Show();
        LoadingPanel.SetProgress(1);

        SoundManager.PlayBg("bgm");

        var pictureId = (int)data;
        Panel.Create((JigsawPanel panel) =>
        {
            jigsawPanel = panel;
            jigsawPanel?.SetData(pictureId);
            LoadingPanel.HideWhenFullProgress();
        }, isAsync: true);
    }

    public override void OnExit()
    {
        jigsawPanel?.Hide();
    }
}