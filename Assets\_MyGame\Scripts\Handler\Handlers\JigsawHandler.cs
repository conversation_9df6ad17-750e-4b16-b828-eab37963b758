﻿internal class JigsawHandler : HandlerBase
{
    private JigsawPanel jigsawPanel;

    public override void OnEnter(object data)
    {
        LoadingPanel.Show();
        LoadingPanel.SetProgress(1);

        SoundManager.PlayBg("bgm");

        var pictureId = (int)data;

        // 检查拼图是否已解锁
        var jigsawSystem = SystemFacade.JigsawSystem;
        if (!jigsawSystem.IsJigsawUnlocked(pictureId))
        {
            Log.Warning($"尝试打开未解锁的拼图: {pictureId}");
            LoadingPanel.Hide();
            HandlerManager.Inst.SwitchHandler<LobbyHandler>();
            return;
        }

        Panel.Create((JigsawPanel panel) =>
        {
            jigsawPanel = panel;
            jigsawPanel?.SetData(pictureId);
            LoadingPanel.HideWhenFullProgress();
        }, isAsync: true);
    }

    public override void OnExit()
    {
        jigsawPanel?.Hide();
    }
}