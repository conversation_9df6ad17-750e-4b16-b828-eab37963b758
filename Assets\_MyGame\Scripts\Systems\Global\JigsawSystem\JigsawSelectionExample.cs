using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 拼图选择面板示例
/// 展示如何在UI中集成拼图系统
/// </summary>
public class JigsawSelectionExample : MonoBehaviour
{
    [Header("UI引用")]
    [SerializeField] private Transform jigsawContainer;
    [SerializeField] private GameObject jigsawItemPrefab;
    
    [Header("显示设置")]
    [SerializeField] private bool showLockedJigsaws = true;
    
    private List<JigsawItemUI> jigsawItems = new List<JigsawItemUI>();
    private JigsawSystem jigsawSystem;

    private void Start()
    {
        jigsawSystem = SystemFacade.JigsawSystem;
        
        // 订阅事件
        jigsawSystem.OnJigsawUnlocked += OnJigsawUnlocked;
        jigsawSystem.OnPieceCountChanged += OnPieceCountChanged;
        
        // 初始化UI
        RefreshJigsawList();
    }

    private void OnDestroy()
    {
        // 取消订阅事件
        if (jigsawSystem != null)
        {
            jigsawSystem.OnJigsawUnlocked -= OnJigsawUnlocked;
            jigsawSystem.OnPieceCountChanged -= OnPieceCountChanged;
        }
    }

    /// <summary>
    /// 刷新拼图列表
    /// </summary>
    public void RefreshJigsawList()
    {
        // 清理现有UI
        ClearJigsawItems();
        
        // 获取拼图数据
        var allJigsaws = jigsawSystem.GetAllJigsawData();
        
        foreach (var jigsawData in allJigsaws)
        {
            // 根据设置决定是否显示锁定的拼图
            if (!showLockedJigsaws && !jigsawData.isUnlocked)
                continue;
                
            CreateJigsawItem(jigsawData);
        }
    }

    /// <summary>
    /// 创建拼图项UI
    /// </summary>
    /// <param name="jigsawData">拼图数据</param>
    private void CreateJigsawItem(JigsawData jigsawData)
    {
        if (jigsawItemPrefab == null || jigsawContainer == null)
            return;

        GameObject itemObj = Instantiate(jigsawItemPrefab, jigsawContainer);
        JigsawItemUI itemUI = itemObj.GetComponent<JigsawItemUI>();
        
        if (itemUI != null)
        {
            itemUI.Setup(jigsawData, this);
            jigsawItems.Add(itemUI);
        }
    }

    /// <summary>
    /// 清理拼图项UI
    /// </summary>
    private void ClearJigsawItems()
    {
        foreach (var item in jigsawItems)
        {
            if (item != null && item.gameObject != null)
                DestroyImmediate(item.gameObject);
        }
        jigsawItems.Clear();
    }

    /// <summary>
    /// 拼图解锁事件处理
    /// </summary>
    /// <param name="jigsawId">解锁的拼图ID</param>
    private void OnJigsawUnlocked(int jigsawId)
    {
        Debug.Log($"拼图 {jigsawId} 已解锁，刷新UI");
        RefreshJigsawList();
    }

    /// <summary>
    /// 拼块数量变化事件处理
    /// </summary>
    /// <param name="newCount">新的拼块数量</param>
    private void OnPieceCountChanged(int newCount)
    {
        // 更新所有拼图项的状态
        foreach (var item in jigsawItems)
        {
            item?.UpdateUnlockStatus();
        }
    }

    /// <summary>
    /// 点击拼图项
    /// </summary>
    /// <param name="jigsawData">拼图数据</param>
    public void OnJigsawItemClicked(JigsawData jigsawData)
    {
        if (jigsawData.isUnlocked)
        {
            // 已解锁，直接进入拼图游戏
            StartJigsawGame(jigsawData.jigsawId);
        }
        else if (jigsawSystem.CanUnlockJigsaw(jigsawData.jigsawId))
        {
            // 可以解锁，显示解锁确认对话框
            ShowUnlockConfirmDialog(jigsawData);
        }
        else
        {
            // 不能解锁，显示提示信息
            ShowInsufficientPiecesMessage(jigsawData);
        }
    }

    /// <summary>
    /// 开始拼图游戏
    /// </summary>
    /// <param name="jigsawId">拼图ID</param>
    private void StartJigsawGame(int jigsawId)
    {
        Debug.Log($"开始拼图游戏: {jigsawId}");
        // 切换到拼图游戏场景
        HandlerManager.Inst.SwitchHandler<JigsawHandler>(jigsawId);
    }

    /// <summary>
    /// 显示解锁确认对话框
    /// </summary>
    /// <param name="jigsawData">拼图数据</param>
    private void ShowUnlockConfirmDialog(JigsawData jigsawData)
    {
        string message = $"是否花费 {jigsawData.needPiece} 个拼块解锁这个拼图？";
        Debug.Log($"显示解锁确认对话框: {message}");
        
        // 这里应该显示实际的确认对话框
        // 示例：
        // ConfirmDialog.Show(message, () => {
        //     if (jigsawSystem.TryUnlockJigsaw(jigsawData.jigsawId))
        //     {
        //         StartJigsawGame(jigsawData.jigsawId);
        //     }
        // });
        
        // 临时直接解锁
        if (jigsawSystem.TryUnlockJigsaw(jigsawData.jigsawId))
        {
            StartJigsawGame(jigsawData.jigsawId);
        }
    }

    /// <summary>
    /// 显示拼块不足提示
    /// </summary>
    /// <param name="jigsawData">拼图数据</param>
    private void ShowInsufficientPiecesMessage(JigsawData jigsawData)
    {
        int needed = jigsawData.needPiece;
        int current = jigsawSystem.GetCurrentPieceCount();
        int shortage = needed - current;
        
        string message = $"拼块不足！还需要 {shortage} 个拼块才能解锁这个拼图。";
        Debug.Log(message);
        
        // 这里应该显示实际的提示消息
        // 例如：ToastMessage.Show(message);
    }

    // 测试方法
    [ContextMenu("添加测试拼块")]
    public void AddTestPieces()
    {
        jigsawSystem.AddPieces(10);
    }

    [ContextMenu("刷新拼图列表")]
    public void RefreshList()
    {
        RefreshJigsawList();
    }
}

/// <summary>
/// 拼图项UI组件
/// </summary>
public class JigsawItemUI : MonoBehaviour
{
    [Header("UI组件")]
    [SerializeField] private UnityEngine.UI.Image jigsawImage;
    [SerializeField] private UnityEngine.UI.Button jigsawButton;
    [SerializeField] private UnityEngine.UI.Text jigsawIdText;
    [SerializeField] private UnityEngine.UI.Text statusText;
    [SerializeField] private UnityEngine.UI.Text requirementText;
    [SerializeField] private GameObject lockOverlay;

    private JigsawData jigsawData;
    private JigsawSelectionExample parentPanel;

    /// <summary>
    /// 设置拼图项
    /// </summary>
    /// <param name="data">拼图数据</param>
    /// <param name="parent">父面板</param>
    public void Setup(JigsawData data, JigsawSelectionExample parent)
    {
        jigsawData = data;
        parentPanel = parent;

        // 设置基本信息
        if (jigsawIdText != null)
            jigsawIdText.text = $"拼图 {data.jigsawId}";

        if (requirementText != null)
            requirementText.text = $"需要 {data.needPiece} 个拼块";

        // 设置按钮事件
        if (jigsawButton != null)
        {
            jigsawButton.onClick.RemoveAllListeners();
            jigsawButton.onClick.AddListener(() => parentPanel.OnJigsawItemClicked(jigsawData));
        }

        // 更新状态
        UpdateUnlockStatus();
    }

    /// <summary>
    /// 更新解锁状态
    /// </summary>
    public void UpdateUnlockStatus()
    {
        if (jigsawData == null) return;

        var jigsawSystem = SystemFacade.JigsawSystem;
        bool isUnlocked = jigsawData.isUnlocked;
        bool canUnlock = jigsawSystem.CanUnlockJigsaw(jigsawData.jigsawId);

        // 更新状态文本
        if (statusText != null)
        {
            if (isUnlocked)
                statusText.text = "已解锁";
            else if (canUnlock)
                statusText.text = "可解锁";
            else
                statusText.text = "未解锁";
        }

        // 更新锁定遮罩
        if (lockOverlay != null)
            lockOverlay.SetActive(!isUnlocked);

        // 更新按钮交互性
        if (jigsawButton != null)
            jigsawButton.interactable = isUnlocked || canUnlock;
    }
}
