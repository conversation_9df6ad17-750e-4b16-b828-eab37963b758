using System;

public class ConfigLoader
{
    private static ConfigLoader _configLoader;
    public static ConfigLoader Inst
    {
        get
        {
            _configLoader ??= new ConfigLoader();
            return _configLoader;
        }
    }

    private readonly TableInfo[] tableNames = new TableInfo[] {
            // new ("Majiang", typeof(ConfigMajiang), typeof(InfoMajiang)),
            new ("Item", typeof(ConfigItem), typeof(InfoItem)),
            new ("Setting", typeof(ConfigSetting), typeof(InfoSetting)),
            new ("Gate", typeof(ConfigGate), typeof(InfoGate)),
            new ("Langui", typeof(ConfigLang), typeof(InfoLang)),
            new ("Title", typeof(ConfigTitle), typeof(InfoTitle)),
            new ("Theme", typeof(ConfigTheme), typeof(InfoTheme)),
            new ("Decorate", typeof(ConfigDecorate), typeof(InfoDecorate)),
            new ("NewFunction", typeof(ConfigNewFunction), typeof(InfoNewFunction)),
            new ("Activity", typeof(ConfigActivity), typeof(InfoActivity)),
            new ("DropItem", typeof(ConfigDropItem), typeof(InfoDropItem)),
            new ("Jigsaw", typeof(ConfigJigsaw), typeof(InfoJigsaw)),
        };

    public TableInfo[] TableNames
    {
        get
        {
            return tableNames;
        }
    }

    public void LoadConfigs(Action OnComplete)
    {
        ConfigManager.ResetAll();
        AssetBundleManager.LoadObject("DataSO/TableSO", (obj) =>
        {
            var tableSo = obj as TableSO;
            for (int i = 0; i < tableNames.Length; i++)
            {
                var tableInfo = TableNames[i];
                var tableName = tableInfo.name;
                if (tableName == "Gate")
                {
                    if (GameGlobal.Channel == "B" || GameGlobal.Channel == "C")
                    {
                        tableName += GameGlobal.Channel;
                    }
                }
                var data = tableSo.GetDatas(tableName);
                ConfigManager.CreateByConfigInfo(tableInfo.configType, data);
            }
            OnComplete?.Invoke();
        });
    }

    public class TableInfo
    {
        public string name;
        public Type configType;
        public Type infoType;
        public TableInfo(string name, Type configType, Type infoType)
        {
            this.name = name;
            this.configType = configType;
            this.infoType = infoType;
        }
    }
}
