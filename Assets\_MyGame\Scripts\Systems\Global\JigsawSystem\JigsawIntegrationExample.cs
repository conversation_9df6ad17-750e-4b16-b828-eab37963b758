using UnityEngine;

/// <summary>
/// 拼图系统集成示例
/// 展示如何在游戏的不同场景中使用拼图系统
/// </summary>
public class JigsawIntegrationExample : MonoBehaviour
{
    [Header("测试配置")]
    [SerializeField] private int testJigsawId = 1;
    [SerializeField] private int testPieceCount = 20;

    private void Start()
    {
        // 在游戏启动时初始化拼图管理器
        JigsawManager.Initialize();
        
        // 显示当前状态
        ShowCurrentStatus();
    }

    private void OnDestroy()
    {
        // 在对象销毁时清理拼图管理器
        JigsawManager.Cleanup();
    }

    /// <summary>
    /// 显示当前拼图系统状态
    /// </summary>
    [ContextMenu("显示当前状态")]
    public void ShowCurrentStatus()
    {
        Debug.Log("=== 拼图系统当前状态 ===");
        Debug.Log($"当前拼块数量: {JigsawManager.System.GetCurrentPieceCount()}");
        Debug.Log($"解锁进度: {JigsawManager.GetProgressInfo()}");
        
        var allJigsaws = JigsawManager.System.GetAllJigsawData();
        foreach (var jigsaw in allJigsaws)
        {
            Debug.Log(JigsawManager.GetJigsawInfo(jigsaw.jigsawId));
        }
    }

    /// <summary>
    /// 模拟玩家完成关卡获得拼块
    /// </summary>
    [ContextMenu("完成关卡获得拼块")]
    public void OnLevelCompleted()
    {
        // 根据关卡难度给予不同数量的拼块
        int rewardPieces = Random.Range(1, 4);
        JigsawManager.RewardPieces(rewardPieces, "完成关卡");
    }

    /// <summary>
    /// 模拟玩家观看广告获得拼块
    /// </summary>
    [ContextMenu("观看广告获得拼块")]
    public void OnWatchAd()
    {
        int adRewardPieces = 5;
        JigsawManager.RewardPieces(adRewardPieces, "观看广告");
    }

    /// <summary>
    /// 模拟玩家尝试解锁拼图
    /// </summary>
    [ContextMenu("尝试解锁拼图")]
    public void TryUnlockJigsaw()
    {
        if (JigsawManager.CanUnlock(testJigsawId))
        {
            if (JigsawManager.TryUnlock(testJigsawId))
            {
                Debug.Log($"成功解锁拼图 {testJigsawId}！");
                // 这里可以播放解锁动画、音效等
                // 可以打开拼图游戏界面
                OpenJigsawGame(testJigsawId);
            }
            else
            {
                Debug.Log($"解锁拼图 {testJigsawId} 失败！");
            }
        }
        else
        {
            var jigsawData = JigsawManager.System.GetJigsawData(testJigsawId);
            if (jigsawData != null)
            {
                int needed = jigsawData.needPiece;
                int current = JigsawManager.System.GetCurrentPieceCount();
                Debug.Log($"拼块不足！需要 {needed} 个，当前只有 {current} 个");
            }
        }
    }

    /// <summary>
    /// 打开拼图游戏
    /// </summary>
    /// <param name="jigsawId">拼图ID</param>
    private void OpenJigsawGame(int jigsawId)
    {
        Debug.Log($"打开拼图游戏，拼图ID: {jigsawId}");
        
        // 这里应该切换到拼图游戏场景
        // 例如：HandlerManager.Inst.SwitchHandler<JigsawHandler>(jigsawId);
        
        // 或者直接创建拼图面板
        // Panel.Create<JigsawPanel>(panel => panel.SetData(jigsawId));
    }

    /// <summary>
    /// 检查可解锁的拼图
    /// </summary>
    [ContextMenu("检查可解锁拼图")]
    public void CheckUnlockableJigsaws()
    {
        var unlockableJigsaws = JigsawManager.GetUnlockableJigsaws();
        
        if (unlockableJigsaws.Count == 0)
        {
            Debug.Log("当前没有可解锁的拼图");
        }
        else
        {
            Debug.Log($"发现 {unlockableJigsaws.Count} 个可解锁的拼图:");
            foreach (var jigsaw in unlockableJigsaws)
            {
                Debug.Log($"- 拼图 {jigsaw.jigsawId} (需要 {jigsaw.needPiece} 个拼块)");
            }
        }
    }

    /// <summary>
    /// 添加测试拼块
    /// </summary>
    [ContextMenu("添加测试拼块")]
    public void AddTestPieces()
    {
        JigsawManager.RewardPieces(testPieceCount, "测试");
    }

    /// <summary>
    /// 重置拼图系统数据（仅用于测试）
    /// </summary>
    [ContextMenu("重置数据（测试用）")]
    public void ResetDataForTesting()
    {
        PlayerPrefsManager.DeleteKey("Jigsaw_SaveData");
        Debug.Log("拼图系统数据已重置，请重启游戏");
    }

    // GUI显示当前状态（仅在开发模式下）
    private void OnGUI()
    {
        if (!Debug.isDebugBuild) return;

        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("拼图系统状态", GUI.skin.box);
        
        var system = JigsawManager.System;
        if (system != null)
        {
            GUILayout.Label($"拼块数量: {system.GetCurrentPieceCount()}");
            GUILayout.Label($"解锁进度: {JigsawManager.GetProgressInfo()}");
            
            if (GUILayout.Button("添加5个拼块"))
            {
                JigsawManager.RewardPieces(5, "GUI测试");
            }
            
            if (GUILayout.Button($"解锁拼图{testJigsawId}"))
            {
                TryUnlockJigsaw();
            }
        }
        
        GUILayout.EndArea();
    }
}
