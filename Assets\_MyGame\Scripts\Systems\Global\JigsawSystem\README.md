# 拼图系统 (JigsawSystem)

## 概述

拼图系统是一个全局系统，用于管理拼图图片的解锁、拼块收集和拼图进度。系统支持通过配置表管理拼图数据，并提供完整的数据持久化功能。

## 核心功能

### 1. 图片ID管理
- 为每张拼图图片分配唯一ID，从1开始递增
- 确保ID的连续性和唯一性
- 支持通过配置表动态添加新拼图

### 2. 拼块收集系统
- 玩家可以通过各种方式获得拼块
- 支持拼块数量的增加和消耗
- 实时更新拼块数量并触发相关事件

### 3. 拼图解锁机制
- 根据配置表中的needPiece字段判断解锁条件
- 玩家需要收集足够的拼块才能解锁对应拼图
- 支持手动解锁，不会自动消耗拼块

### 4. 数据持久化
- 自动保存和加载拼图解锁状态
- 保存当前拼块数量
- 使用PlayerPrefs进行本地存储

## 配置结构

### ConfigJigsaw (配置类)
```csharp
public class ConfigJigsaw : ConfigBase
{
    public static InfoJigsaw GetData(int id);
    public static List<InfoJigsaw> GetJigsaws();
}
```

### InfoJigsaw (数据结构)
```csharp
public class InfoJigsaw : ConfigInfoBase
{
    public int id;          // 拼图唯一标识符
    public int width;       // 拼图宽度（拼块数）
    public int height;      // 拼图高度（拼块数）
    public int needPiece;   // 解锁所需拼块数量
}
```

### 配置表格式 (Jigsaw.json)
```json
[
  {
    "id": 1,
    "width": 6,
    "height": 8,
    "needPiece": 15
  },
  {
    "id": 2,
    "width": 6,
    "height": 8,
    "needPiece": 20
  }
]
```

## 系统接口

### JigsawSystem (核心系统)

#### 拼块管理
- `AddPieces(int count)` - 添加拼块
- `ConsumePieces(int count)` - 消耗拼块
- `GetCurrentPieceCount()` - 获取当前拼块数量

#### 拼图管理
- `IsJigsawUnlocked(int jigsawId)` - 检查拼图是否已解锁
- `CanUnlockJigsaw(int jigsawId)` - 检查是否可以解锁拼图
- `TryUnlockJigsaw(int jigsawId)` - 尝试解锁拼图
- `GetJigsawData(int jigsawId)` - 获取拼图数据

#### 数据查询
- `GetAllJigsawData()` - 获取所有拼图数据
- `GetUnlockedJigsaws()` - 获取已解锁的拼图
- `GetLockedJigsaws()` - 获取未解锁的拼图

#### 事件系统
- `OnJigsawUnlocked` - 拼图解锁事件
- `OnPieceCountChanged` - 拼块数量变化事件

### JigsawManager (管理器)

提供更高级的接口和便捷方法：

- `Initialize()` - 初始化系统
- `Cleanup()` - 清理系统
- `RewardPieces(int count, string reason)` - 奖励拼块
- `CanUnlock(int jigsawId)` - 检查是否可解锁
- `TryUnlock(int jigsawId)` - 尝试解锁
- `GetUnlockableJigsaws()` - 获取可解锁的拼图列表
- `GetProgressInfo()` - 获取解锁进度信息

## 使用示例

### 基本使用
```csharp
// 获取系统实例
var jigsawSystem = SystemFacade.JigsawSystem;

// 添加拼块
jigsawSystem.AddPieces(10);

// 检查是否可以解锁拼图
if (jigsawSystem.CanUnlockJigsaw(1))
{
    // 尝试解锁
    if (jigsawSystem.TryUnlockJigsaw(1))
    {
        Debug.Log("拼图解锁成功！");
    }
}
```

### 使用管理器
```csharp
// 初始化（在游戏启动时）
JigsawManager.Initialize();

// 奖励拼块
JigsawManager.RewardPieces(5, "完成关卡");

// 检查可解锁的拼图
var unlockableJigsaws = JigsawManager.GetUnlockableJigsaws();
foreach (var jigsaw in unlockableJigsaws)
{
    Debug.Log($"可解锁拼图: {jigsaw.jigsawId}");
}
```

### 事件处理
```csharp
// 订阅事件
jigsawSystem.OnJigsawUnlocked += (jigsawId) => {
    Debug.Log($"拼图 {jigsawId} 已解锁！");
    // 播放解锁动画、音效等
};

jigsawSystem.OnPieceCountChanged += (newCount) => {
    Debug.Log($"拼块数量: {newCount}");
    // 更新UI显示
};
```

## 集成指南

### 1. 配置表设置
1. 在 `Excels/excel/Jigsaw.xlsx` 中配置拼图数据
2. 运行导出脚本生成 `Jigsaw.json`
3. 系统会自动加载配置数据

### 2. 系统初始化
在游戏启动时调用：
```csharp
JigsawManager.Initialize();
```

### 3. 拼块奖励集成
在适当的游戏事件中奖励拼块：
```csharp
// 完成关卡
JigsawManager.RewardPieces(3, "完成关卡");

// 观看广告
JigsawManager.RewardPieces(5, "观看广告");

// 每日签到
JigsawManager.RewardPieces(2, "每日签到");
```

### 4. UI集成
在拼图相关的UI中使用系统接口：
```csharp
// 显示当前拼块数量
int pieceCount = JigsawManager.System.GetCurrentPieceCount();

// 显示解锁进度
string progress = JigsawManager.GetProgressInfo();

// 检查拼图状态
bool isUnlocked = JigsawManager.System.IsJigsawUnlocked(jigsawId);
```

## 注意事项

1. **数据一致性**: 系统会自动处理配置表变化，确保数据一致性
2. **性能考虑**: 系统使用字典缓存数据，查询性能良好
3. **扩展性**: 可以轻松添加新的拼图类型和属性
4. **调试支持**: 提供详细的调试信息和测试工具

## 测试工具

项目包含以下测试工具：
- `JigsawSystemTest` - 基本功能测试
- `JigsawIntegrationExample` - 集成示例和GUI测试界面

使用这些工具可以快速验证系统功能和进行调试。
