using System;
using LitJson;

[Serializable]
public class InfoJigsaw : ConfigInfoBase
{
    public int id;
    public int width;
    public int height;
    public int needPiece;

    public override object GetKey(int index)
    {
        return id;
    }

    public override void Parse(JsonData json)
    {
        id = JsonUtil.ToInt(json, "id");
        width = JsonUtil.ToInt(json, "width");
        height = JsonUtil.ToInt(json, "height");
        needPiece = JsonUtil.ToInt(json, "needPiece");
    }
}
