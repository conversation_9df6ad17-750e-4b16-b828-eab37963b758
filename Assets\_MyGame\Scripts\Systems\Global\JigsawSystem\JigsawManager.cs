using System;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 拼图管理器，提供拼图系统的高级接口
/// </summary>
public static class JigsawManager
{
    /// <summary>
    /// 获取拼图系统实例
    /// </summary>
    public static JigsawSystem System => SystemFacade.JigsawSystem;

    /// <summary>
    /// 初始化拼图系统（在游戏启动时调用）
    /// </summary>
    public static void Initialize()
    {
        var system = System;
        system.OnJigsawUnlocked += OnJigsawUnlocked;
        system.OnPieceCountChanged += OnPieceCountChanged;
    }

    /// <summary>
    /// 清理拼图系统（在游戏关闭时调用）
    /// </summary>
    public static void Cleanup()
    {
        var system = System;
        if (system != null)
        {
            system.OnJigsawUnlocked -= OnJigsawUnlocked;
            system.OnPieceCountChanged -= OnPieceCountChanged;
        }
    }

    /// <summary>
    /// 奖励玩家拼块
    /// </summary>
    /// <param name="count">拼块数量</param>
    /// <param name="reason">获得原因</param>
    public static void RewardPieces(int count, string reason = "")
    {
        if (count <= 0) return;

        System.AddPieces(count);
        Debug.Log($"获得 {count} 个拼块" + (string.IsNullOrEmpty(reason) ? "" : $" ({reason})"));
    }

    /// <summary>
    /// 检查是否可以解锁指定拼图
    /// </summary>
    /// <param name="jigsawId">拼图ID</param>
    /// <returns>是否可以解锁</returns>
    public static bool CanUnlock(int jigsawId)
    {
        return System.CanUnlockJigsaw(jigsawId);
    }

    /// <summary>
    /// 尝试解锁拼图
    /// </summary>
    /// <param name="jigsawId">拼图ID</param>
    /// <returns>是否成功解锁</returns>
    public static bool TryUnlock(int jigsawId)
    {
        return System.TryUnlockJigsaw(jigsawId);
    }

    /// <summary>
    /// 获取所有可解锁的拼图
    /// </summary>
    /// <returns>可解锁的拼图列表</returns>
    public static List<JigsawData> GetUnlockableJigsaws()
    {
        var allJigsaws = System.GetAllJigsawData();
        var unlockable = new List<JigsawData>();

        foreach (var jigsaw in allJigsaws)
        {
            if (!jigsaw.isUnlocked && System.CanUnlockJigsaw(jigsaw.jigsawId))
            {
                unlockable.Add(jigsaw);
            }
        }

        return unlockable;
    }

    /// <summary>
    /// 获取拼图解锁进度信息
    /// </summary>
    /// <returns>进度信息字符串</returns>
    public static string GetProgressInfo()
    {
        var allJigsaws = System.GetAllJigsawData();
        var unlockedCount = 0;
        
        foreach (var jigsaw in allJigsaws)
        {
            if (jigsaw.isUnlocked)
                unlockedCount++;
        }

        return $"{unlockedCount}/{allJigsaws.Count}";
    }

    /// <summary>
    /// 拼图解锁事件处理
    /// </summary>
    /// <param name="jigsawId">解锁的拼图ID</param>
    private static void OnJigsawUnlocked(int jigsawId)
    {
        Debug.Log($"拼图 {jigsawId} 已解锁！");
        
        // 这里可以添加解锁特效、音效等
        // 可以触发UI更新
        // 可以发送统计数据等
    }

    /// <summary>
    /// 拼块数量变化事件处理
    /// </summary>
    /// <param name="newCount">新的拼块数量</param>
    private static void OnPieceCountChanged(int newCount)
    {
        Debug.Log($"拼块数量变化: {newCount}");
        
        // 这里可以更新UI显示
        // 检查是否有新的拼图可以解锁
        var unlockableCount = GetUnlockableJigsaws().Count;
        if (unlockableCount > 0)
        {
            Debug.Log($"有 {unlockableCount} 个拼图可以解锁！");
        }
    }

    /// <summary>
    /// 获取拼图详细信息
    /// </summary>
    /// <param name="jigsawId">拼图ID</param>
    /// <returns>拼图信息</returns>
    public static string GetJigsawInfo(int jigsawId)
    {
        var jigsawData = System.GetJigsawData(jigsawId);
        if (jigsawData == null)
            return "拼图不存在";

        var status = jigsawData.isUnlocked ? "已解锁" : "未解锁";
        var canUnlock = System.CanUnlockJigsaw(jigsawId) ? "可解锁" : "不可解锁";
        
        return $"拼图 {jigsawId}: {status}, 需要 {jigsawData.needPiece} 个拼块, {canUnlock}";
    }
}
