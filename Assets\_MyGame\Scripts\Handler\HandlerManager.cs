﻿using System;
using System.Collections.Generic;
using UnityEngine;

public class HandlerManager : MonoBehaviour
{
    private static HandlerManager _instance;
    public static HandlerManager Inst
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindObjectOfType<HandlerManager>();
                if (_instance == null)
                {
                    GameObject obj = new GameObject("HandlerManager");
                    _instance = obj.AddComponent<HandlerManager>();
                }
            }
            return _instance;
        }
    }

    private void Awake()
    {
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private HandlerBase curHandler;
    internal void SwitchHandler<T>() where T : HandlerBase
    {
        if (curHandler != null)
        {
            curHandler.OnExit();
            Destroy(curHandler);
        }
        curHandler = gameObject.AddComponent<T>();
        curHandler.OnEnter();
    }
    internal void SwitchHandler<T>(object data) where T : HandlerBase
    {
        if (curHandler != null)
        {
            curHandler.OnExit();
            Destroy(curHandler);
        }
        curHandler = gameObject.AddComponent<T>();
        curHandler.OnEnter(data);
    }

    internal bool IsCurrentHandler<T>() where T : HandlerBase
    {
        return curHandler != null && curHandler.GetType() == typeof(T);
    }



}