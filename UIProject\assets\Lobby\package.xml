<?xml version="1.0" encoding="utf-8"?>
<packageDescription id="d0htg1xd">
  <resources>
    <image id="f2dp0" name="btnStart.png" path="/images/"/>
    <component id="p6dv1" name="LobbyPanel.xml" path="/" exported="true"/>
    <image id="m6gv2" name="levelBg.png" path="/images/" scale="9grid" scale9grid="31,26,107,1"/>
    <image id="ibx734" name="btnClose.png" path="/images/"/>
    <image id="ss2k3j" name="btnSetting.png" path="/images/"/>
    <image id="ss2k3n" name="btnShop.png" path="/images/"/>
    <image id="ss2k3o" name="levelRewardBar.png" path="/images/"/>
    <image id="ss2k3p" name="pigBar.png" path="/images/"/>
    <image id="ss2k3q" name="progressBar.png" path="/images/"/>
    <image id="ss2k3r" name="progressBg.png" path="/images/" scale="9grid" scale9grid="15,0,3,26"/>
    <image id="ss2k3s" name="btnGet.png" path="/images/"/>
    <image id="ss2k3u" name="pigNumBg.png" path="/images/"/>
    <image id="ss2k3v" name="pigOnceBg.png" path="/images/"/>
    <image id="ss2k3w" name="pigProgressBar.png" path="/images/"/>
    <image id="ss2k3x" name="pigProgressBg.png" path="/images/"/>
    <image id="ss2k3y" name="pigTitle.png" path="/images/"/>
    <image id="ss2k3z" name="pigImg.png" path="/images/"/>
    <image id="t5i145" name="image_titleHp.png" path="/images/"/>
    <image id="t5i146" name="image_bg.png" path="/images/" scale="9grid" scale9grid="20,19,6,8"/>
    <image id="t5i147" name="image_line.png" path="/images/" scale="tile"/>
    <image id="t5i148" name="image_bg_green.png" path="/images/" scale="9grid" scale9grid="33,0,8,61"/>
    <image id="t5i14b" name="image_btnText1.png" path="/images/"/>
    <image id="t5i14d" name="image_line2.png" path="/images/"/>
    <image id="t5i14e" name="image_line3.png" path="/images/"/>
    <image id="t5i14i" name="image_boxReward_arrow.png" path="/images/" duplicatePadding="true"/>
    <image id="t5i14j" name="image_boxReward_bg.png" path="/images/" scale="9grid" scale9grid="31,31,6,4"/>
    <image id="llqa4p" name="image_countBg.png" path="/images/"/>
    <image id="llqa4q" name="image_gateBg.png" path="/images/" scale="9grid" scale9grid="33,0,5,64"/>
    <image id="llqa4r" name="image_titleBg.png" path="/images/" scale="9grid" scale9grid="13,0,8,64"/>
    <image id="habh5m" name="image_titleBg2.png" path="/images/"/>
    <component id="mero5o" name="BtnScenePrg.xml" path="/"/>
    <image id="mero5p" name="prg_title_bar.png" path="/images/" scale="9grid" scale9grid="7,0,2,13"/>
    <image id="mero5q" name="prg_title_bg.png" path="/images/" scale="9grid" scale9grid="11,0,3,19"/>
    <component id="mero5r" name="PrgSceneCollect.xml" path="/"/>
    <image id="p1mo5t" name="image_titleBg3.png" path="/images/" scale="9grid" scale9grid="13,15,2,17"/>
    <component id="p1mo5w" name="GetStarPanel.xml" path="/" exported="true"/>
    <image id="p1mo5x" name="image_getStarDec.png" path="/images/" duplicatePadding="true"/>
    <component id="p1mo5y" name="BtnStart.xml" path="/" exported="true"/>
    <component id="kq0365" name="Empty.xml" path="/"/>
    <image id="bn6m6d" name="image_lock.png" path="/images/"/>
    <image id="bn6m6e" name="image_cityNameBg.png" path="/images/" scale="9grid" scale9grid="16,19,15,9"/>
    <component id="ku5i6h" name="Star.xml" path="/" exported="true"/>
    <image id="mfj76n" name="123.png" path="/images/"/>
    <image id="mn346p" name="btn_com.png" path="/images/" scale="9grid" scale9grid="40,39,5,5"/>
    <component id="mn346q" name="BtnCom.xml" path="/"/>
    <image id="mn346r" name="btn_com2.png" path="/images/" scale="9grid" scale9grid="40,41,5,3"/>
    <component id="fe0r6s" name="DailyChallengePanel.xml" path="/" exported="true"/>
    <image id="fe0r6x" name="image_mark.png" path="/images/"/>
    <image id="fe0r6t" name="city2.png" path="/images/"/>
    <image id="fe0r6u" name="btn_start.png" path="/images/" scale="9grid" scale9grid="75,0,7,145"/>
    <image id="fe0r6v" name="btn_addDesktop.png" path="/images/"/>
    <image id="fe0r6w" name="btn_dailyChallenge.png" path="/images/"/>
    <image id="fe0r6y" name="btn_invite.png" path="/images/"/>
    <image id="fe0r6z" name="btn_rank.png" path="/images/"/>
    <image id="fe0r70" name="btn_theme.png" path="/images/"/>
    <image id="fe0r71" name="btn_dailyReward.png" path="/images/"/>
    <image id="fe0r72" name="city3.png" path="/images/"/>
    <image id="fe0r73" name="btn_cityPrg.png" path="/images/"/>
    <image id="fe0r74" name="image_text_1.png" path="/images/"/>
    <image id="fe0r75" name="image_text_2.png" path="/images/"/>
    <image id="fe0r76" name="image_text_3.png" path="/images/"/>
    <misc id="ryhb77" name="show_decoration.atlas" path="/spine/"/>
    <image id="ryhb78" name="show_decoration.png" path="/spine/"/>
    <spine id="ryhb79" name="show_decoration.skel" path="/spine/" width="463" height="196" require="ryhb77,ryhb78" atlasNames="show_decoration" anchor="231,98" shader="FairyGUI/Image"/>
    <component id="ryhb7a" name="ShowEffect.xml" path="/" exported="true"/>
    <component id="xanh7p" name="City3.xml" path="/city_bak/"/>
    <image id="7nup80" name="city1.png" path="/images/"/>
    <component id="7nup81" name="City1.xml" path="/city_bak/"/>
    <component id="7nup82" name="City2.xml" path="/city_bak/"/>
    <component id="nvpe83" name="City4.xml" path="/city_bak/"/>
    <image id="nvpe85" name="city4.png" path="/images/"/>
    <image id="xigm8g" name="124.png" path="/images/"/>
    <component id="ihei8h" name="CityComplete.xml" path="/" exported="true"/>
    <image id="ihei8i" name="image_cityComplete.png" path="/images/"/>
    <image id="ihei8j" name="131.png" path="/images/"/>
    <image id="ihei8l" name="city5.png" path="/images/"/>
    <component id="ihei8m" name="City5.xml" path="/city_bak/"/>
    <image id="czfy8x" name="btn_sidebar.png" path="/images/"/>
    <image id="ip8m8y" name="btn_addDesktopDY.png" path="/images/"/>
    <image id="vlmw8z" name="btn_dyFeed.png" path="/images/"/>
    <component id="rev190" name="BtnCom2.xml" path="/"/>
    <image id="rev192" name="image_btnTextBg.png" path="/images/" scale="9grid" scale9grid="15,0,6,28"/>
    <component id="rev193" name="BtnDailyBattle.xml" path="/"/>
    <image id="rev194" name="btn_dailyBattleBg.png" path="/images/" scale="9grid" scale9grid="159,0,11,145"/>
    <image id="rev195" name="image_btnTextBg2.png" path="/images/" scale="9grid" scale9grid="17,0,4,33"/>
    <image id="rev196" name="image_book.png" path="/images/" atlas="alone_npot"/>
    <image id="rev197" name="lobbyBg.png" path="/images/" atlas="alone_npot"/>
    <component id="iwnd98" name="BtnArrow.xml" path="/"/>
    <image id="iwnd99" name="btn_aoorw1.png" path="/images/"/>
    <image id="iwnd9a" name="btn_aoorw2.png" path="/images/"/>
    <misc id="v6dr9b" name="ChengShiShouJiWC.atlas" path="/spine/"/>
    <image id="v6dr9c" name="ChengShiShouJiWC.png" path="/spine/"/>
    <spine id="v6dr9d" name="ChengShiShouJiWC.skel" path="/spine/" width="720" height="1280" require="v6dr9b,v6dr9c" atlasNames="ChengShiShouJiWC" anchor="360,740" shader="FairyGUI/Image"/>
    <component id="v6dr9g" name="ListItem.xml" path="/"/>
    <misc id="volt9h" name="anniu.atlas" path="/spine/"/>
    <image id="volt9i" name="anniu.png" path="/spine/"/>
    <spine id="volt9j" name="anniu.skel" path="/spine/" width="400" height="170" require="volt9h,volt9i" atlasNames="anniu" anchor="199,82" shader="FairyGUI/Image"/>
    <component id="nns29k" name="JigsawContainer.xml" path="/" exported="true"/>
    <image id="nns29l" name="picture1.jpg" path="/images/"/>
    <component id="nns29m" name="JigsawItem.xml" path="/"/>
  </resources>
  <publish name=""/>
</packageDescription>