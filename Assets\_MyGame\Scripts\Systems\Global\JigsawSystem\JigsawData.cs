using System;
using System.Collections.Generic;
using System.Linq;

[Serializable]
public class JigsawSaveData
{
    public int[] jigsawIds;
    public bool[] unlockedStates;
    public int currentPieceCount;

    [NonSerialized]
    public Dictionary<int, bool> unlockedJigsaws = new Dictionary<int, bool>();

    public void OnAfterDeserialize()
    {
        unlockedJigsaws = new Dictionary<int, bool>();
        if (jigsawIds != null && unlockedStates != null && jigsawIds.Length == unlockedStates.Length)
        {
            for (int i = 0; i < jigsawIds.Length; i++)
            {
                unlockedJigsaws[jigsawIds[i]] = unlockedStates[i];
            }
        }
    }

    public void OnBeforeSerialize()
    {
        if (unlockedJigsaws != null)
        {
            jigsawIds = unlockedJigsaws.Keys.ToArray();
            unlockedStates = unlockedJigsaws.Values.ToArray();
        }
    }
}

[Serializable]
public class JigsawData
{
    public int jigsawId;
    public bool isUnlocked;
    public int requiredPieces;

    private InfoJigsaw _info;
    public InfoJigsaw info => _info ?? (_info = ConfigJigsaw.GetData(jigsawId));

    public int width => info?.width ?? 6;
    public int height => info?.height ?? 8;
    public int needPiece => info?.needPiece ?? 15;

    public JigsawData(int id)
    {
        jigsawId = id;
        isUnlocked = false;
        var jigsawInfo = ConfigJigsaw.GetData(id);
        requiredPieces = jigsawInfo?.needPiece ?? 15;
    }

    public bool CanUnlock(int currentPieceCount)
    {
        return currentPieceCount >= requiredPieces;
    }
}
