using UnityEngine;

public class JigsawSystemTest : MonoBehaviour
{
    [ContextMenu("Test Jigsaw System")]
    public void TestJigsawSystem()
    {
        var jigsawSystem = SystemFacade.JigsawSystem;
        
        Debug.Log("=== 拼图系统测试 ===");
        Debug.Log($"当前拼块数量: {jigsawSystem.GetCurrentPieceCount()}");
        
        // 测试添加拼块
        jigsawSystem.AddPieces(20);
        Debug.Log($"添加20个拼块后: {jigsawSystem.GetCurrentPieceCount()}");
        
        // 获取所有拼图数据
        var allJigsaws = jigsawSystem.GetAllJigsawData();
        Debug.Log($"总拼图数量: {allJigsaws.Count}");
        
        foreach (var jigsaw in allJigsaws)
        {
            Debug.Log($"拼图ID: {jigsaw.jigsawId}, 需要拼块: {jigsaw.needPiece}, 已解锁: {jigsaw.isUnlocked}");
            
            if (jigsawSystem.CanUnlockJigsaw(jigsaw.jigsawId))
            {
                Debug.Log($"可以解锁拼图 {jigsaw.jigsawId}");
            }
        }
        
        // 尝试解锁第一个拼图
        if (allJigsaws.Count > 0)
        {
            var firstJigsaw = allJigsaws[0];
            if (jigsawSystem.TryUnlockJigsaw(firstJigsaw.jigsawId))
            {
                Debug.Log($"成功解锁拼图 {firstJigsaw.jigsawId}");
            }
            else
            {
                Debug.Log($"无法解锁拼图 {firstJigsaw.jigsawId}");
            }
        }
        
        Debug.Log($"解锁后拼块数量: {jigsawSystem.GetCurrentPieceCount()}");
        Debug.Log(jigsawSystem.GetDebugInfo());
    }
}
