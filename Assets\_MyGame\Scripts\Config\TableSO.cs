using System;
using System.Collections.Generic;
using LitJson;
using UnityEngine;

[Serializable]
public class TableSO : ScriptableObject
{
    public InfoGate[] gates;
    public InfoGate[] gatesB;
    public InfoGate[] gatesC;
    public InfoItem[] items;
    public InfoSetting[] settings;
    public InfoLang[] languis;
    public InfoTitle[] titles;
    public InfoTheme[] themes;
    public InfoDecorate[] decorates;
    public InfoNewFunction[] newFunctions;
    public InfoActivity[] activities;
    public InfoDropItem[] dropItems;
    public InfoJigsaw[] jigsaws;
    public ConfigInfoBase[] GetDatas(string tableName)
    {
        return tableName switch
        {
            "Gate" => gates,
            "GateB" => gatesB,
            "GateC" => gatesC,
            "Item" => items,
            "Setting" => settings,
            "Langui" => languis,
            "Title" => titles,
            "Theme" => themes,
            "Decorate" => decorates,
            "NewFunction" => newFunctions,
            "Activity" => activities,
            "DropItem" => dropItems,
            "Jigsaw" => jigsaws,
            _ => throw new NotImplementedException(),
        };
    }

    //以下逻辑只在编辑器中有效
#if UNITY_EDITOR
    public static string[] tableNames = new string[] {
        "Gate",
        "GateB",
        "GateC",
        "Item",
        "Setting",
        "Langui",
        "Title",
        "Theme",
        "Decorate",
        "NewFunction",
        "Activity",
        "DropItem",
        "Jigsaw",
    };

    public void GenerateSO(string tableName, JsonData json)
    {
        switch (tableName)
        {
            case "Gate":
                gates = CreateArray<InfoGate>(json);
                break;
            case "GateB":
                gatesB = CreateArray<InfoGate>(json);
                break;
            case "GateC":
                gatesC = CreateArray<InfoGate>(json);
                break;
            case "Item":
                items = CreateArray<InfoItem>(json);
                break;
            case "Setting":
                settings = CreateArray<InfoSetting>(json);
                break;
            case "Langui":
                languis = CreateArray<InfoLang>(json);
                break;
            case "Title":
                titles = CreateArray<InfoTitle>(json);
                break;
            case "Theme":
                themes = CreateArray<InfoTheme>(json);
                break;
            case "Decorate":
                decorates = CreateArray<InfoDecorate>(json);
                break;
            case "NewFunction":
                newFunctions = CreateArray<InfoNewFunction>(json);
                break;
            case "Activity":
                activities = CreateArray<InfoActivity>(json);
                break;
            case "DropItem":
                dropItems = CreateArray<InfoDropItem>(json);
                break;
            case "Jigsaw":
                jigsaws = CreateArray<InfoJigsaw>(json);
                break;
        }
    }

    public static T[] CreateArray<T>(JsonData json) where T : ConfigInfoBase, new()
    {
        var list = new List<T>();
        if (json.IsArray)
        {
            for (int i = 0; i < json.Count; i++)
            {
                T data = new T();
                data.Parse(json[i]);
                data.uniqueKey = data.GetKey(i).ToString();
                list.Add(data);
            }
        }
        else
        {
            string[] keys = new string[json.Count];
            json.Keys.CopyTo(keys, 0);
            for (int i = 0; i < keys.Length; i++)
            {
                string key = keys[i];
                T data = new T();
                data.Parse(json[key]);
                data.uniqueKey = data.GetKey(i).ToString();
                list.Add(data);
            }
        }
        var result = list.ToArray();
        return result;
    }
#endif
}