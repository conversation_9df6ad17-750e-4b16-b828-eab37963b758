using LitJson;
using System;
using System.Collections.Generic;

public class ConfigBase
{
    protected Dictionary<object, ConfigInfoBase> cacheDic = new Dictionary<object, ConfigInfoBase>();
    public void Parse<T>(string jsonStr) where T : ConfigInfoBase, new()
    {
        Parse(typeof(T), jsonStr);
    }

    internal void Parse(Type infoType, string text)
    {
        Reset();
        var json = JsonMapper.ToObject(text);
        for (var i = 0; i < json.Count; i++)
        {
            var item = json[i];

            var info = Activator.CreateInstance(infoType) as ConfigInfoBase;
            info.Parse(item);
            CacheData(info.GetKey(i), info);
        }
    }

    public virtual void Reset()
    {
        cacheDic.Clear();
    }
    internal virtual void CacheData(object key, ConfigInfoBase info)
    {
        cacheDic.Add(key, info);
    }

    public T GetData<T>(object key) where T : ConfigInfoBase
    {
        cacheDic.TryGetValue(key.ToString(), out ConfigInfoBase value);
        return (T)value;
    }

    public List<T> GetList<T>() where T : ConfigInfoBase
    {
        var result = new List<T>();
        foreach (var item in cacheDic)
        {
            result.Add((T)item.Value);
        }
        return result;
    }
}